import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(
  request: NextRequest,
  { params }: { params: { uuid: string } }
) {
  try {
    const { uuid } = params;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(uuid)) {
      return NextResponse.json(
        { error: 'Invalid UUID format' },
        { status: 400 }
      );
    }

    // Fetch search results from database
    const { data: searchResult, error } = await supabase
      .from('search_results')
      .select('*')
      .eq('id', uuid)
      .gt('expires_at', new Date().toISOString()) // Only non-expired results
      .single();

    if (error || !searchResult) {
      console.log(`Search result not found or expired for UUID: ${uuid}`);
      return NextResponse.json(
        { 
          error: 'Search results not found or have expired',
          message: 'The search results you are looking for may have expired or do not exist. Search results are available for 24 hours after creation.'
        },
        { status: 404 }
      );
    }

    // Update access tracking (async, don't wait for completion)
    supabase
      .from('search_results')
      .update({
        accessed_count: (searchResult.accessed_count || 0) + 1,
        last_accessed_at: new Date().toISOString()
      })
      .eq('id', uuid)
      .then(({ error: updateError }) => {
        if (updateError) {
          console.error('Error updating access tracking:', updateError);
        }
      });

    // Return the stored search results
    return NextResponse.json({
      ...searchResult.results,
      uuid: searchResult.id,
      originalQuery: searchResult.query,
      createdAt: searchResult.created_at,
      accessCount: searchResult.accessed_count + 1,
      isSharedResult: true
    });

  } catch (error) {
    console.error('Error retrieving search results:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
